# 📊 Reporte de Comparación de Esquemas
## Homologacion vs Produccion

**Fecha de generación:** 2025-08-26 09:55:36

## 📋 Resumen Ejecutivo

| Métrica | Valor |
|---------|-------|
| Tablas en homologacion | 1,496 |
| Tablas en produccion | 1,500 |
| Tablas comunes | 1,495 |
| Tablas solo en homologacion | 1 |
| Tablas solo en produccion | 5 |
| Tablas con diferencias en columnas | 10 |

### Estado General: ❌ **LOS ESQUEMAS NO SON IDÉNTICOS**
### Nivel de Criticidad: 🟠 MEDIO

## 🚨 Tablas Solo en Homologacion (1)

| Tabla | Schema | Criticidad |
|-------|--------|------------|
| `recibo_apert_agb` | `tensoft` | 🟡 ALTO |

## 🚨 Tablas Solo en Produccion (5)

| Tabla | Schema | Criticidad |
|-------|--------|------------|
| `app_auxiliar_numera` | `ressia` | 🟠 MEDIO |
| `lolo` | `ressia` | 🟠 MEDIO |
| `paso1` | `ressia` | 🟠 MEDIO |
| `rec` | `ressia` | 🟠 MEDIO |
| `temp_nro_doc` | `tensoft` | 🟡 ALTO |

## 🔧 Diferencias en Columnas (10 tablas afectadas)

### 📋 `tensoft.calendario`

**Columnas solo en produccion:**
- `unique` - `(empresa,periodo_facturacion,id_grupo) constraint "tensoft".unique_calendario`

### 📋 `lobatose.oracle_transac_clie`

**Diferencias de tipo:**

| Columna | Homologacion | Produccion | Criticidad |
|---------|-------------|-----------|------------|
| `cai_cae` | `char(30)` | `varchar(50)` | 🟡 ALTO |

### 📋 `ressia.oracle_transaccion_auxiliar_v3`

**Diferencias de tipo:**

| Columna | Homologacion | Produccion | Criticidad |
|---------|-------------|-----------|------------|
| `cai_cae` | `char(30)` | `varchar(50)` | 🟡 ALTO |

### 📋 `tensoft.fac_impresion`

**Columnas solo en produccion:**
- `estado_sgd` - `varchar(25)`

**Diferencias de tipo:**

| Columna | Homologacion | Produccion | Criticidad |
|---------|-------------|-----------|------------|
| `codigo_cae` | `varchar(14)` | `varchar(100)` | 🟡 ALTO |
| `motivo_afip` | `varchar(3)` | `char(100)` | 🟡 ALTO |

### 📋 `ressia.cloud_tipo_linea_v2`

**Diferencias de tipo:**

| Columna | Homologacion | Produccion | Criticidad |
|---------|-------------|-----------|------------|
| `cai_cae` | `char(30)` | `varchar(50)` | 🟡 ALTO |

### 📋 `ressia.oracle_ana_interna`

**Columnas solo en produccion:**
- `py_imputa` - `varchar(20)`

### 📋 `ressia.oracle_transaccion_auxiliar_v2`

**Diferencias de tipo:**

| Columna | Homologacion | Produccion | Criticidad |
|---------|-------------|-----------|------------|
| `cai_cae` | `char(30)` | `varchar(50)` | 🟡 ALTO |

### 📋 `ressia.cloud_tipo_tax_v2`

**Diferencias de tipo:**

| Columna | Homologacion | Produccion | Criticidad |
|---------|-------------|-----------|------------|
| `cai_cae` | `char(30)` | `varchar(50)` | 🟡 ALTO |

### 📋 `tensoft.factura_historico_sgd`

**Columnas solo en homologacion:**
- `cdc` - `varchar(100)`

**Columnas solo en produccion:**
- `crc` - `varchar(100)`

### 📋 `ressia.cli_var_normalizada`

**Diferencias de tipo:**

| Columna | Homologacion | Produccion | Criticidad |
|---------|-------------|-----------|------------|
| `codigo_direccion` | `varchar(20)` | `varchar(200)` | 🟡 ALTO |

## 🎯 Recomendaciones de Sincronización

### 🟠 MEDIO Crear tablas faltantes en Produccion
- **Acción:** Crear 1 tablas que existen en homologacion pero no en produccion
- **Impacto:** Funcionalidades pueden fallar si estas tablas son requeridas

### 🟠 MEDIO Evaluar tablas extra en Produccion
- **Acción:** Revisar 5 tablas que existen solo en produccion
- **Impacto:** Determinar si deben agregarse a homologacion o eliminarse de produccion

### 🔴 CRÍTICO Sincronizar columnas
- **Acción:** Revisar y sincronizar diferencias en 10 tablas
- **Impacto:** Consultas y aplicaciones pueden fallar por columnas faltantes o tipos incorrectos
