# 📊 Reporte Consolidado de Esquemas
## Desarrollo vs Homologación vs Producción

**Fecha de generación:** 2025-08-26 09:55:36

## 📋 Resumen General

| Ambiente | Tablas | Procedures | Row Types |
|----------|--------|------------|-----------|
| Desarrollo | 1,510 | 0 | 0 |
| Homologacion | 1,496 | 0 | 0 |
| Produccion | 1,500 | 0 | 0 |

## 🔍 Análisis de Consistencia

### ✅ Tablas Consistentes en Todos los Ambientes: 1,495

### ❌ Tablas Inconsistentes: 17

| Tabla | Presente en | Falta en | Criticidad |
|-------|-------------|----------|------------|
| `ressia.app_auxiliar_numera` | desarrollo, produccion | homologacion | 🟠 MEDIO |
| `ressia.lolo` | desarrollo, produccion | homologacion | 🟠 MEDIO |
| `ressia.paso1` | produccion | desarrollo, homologacion | 🟡 ALTO |
| `ressia.rec` | desarrollo, produccion | homologacion | 🟠 MEDIO |
| `ressia.sol_hoja_ruta` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `ressia.sol_rel` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.calendario_segmento` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.ciudad_sigii_sol` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.cotiz_extendida_v1` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.modalidad` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.provin_sigii_sol` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.receipts` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.recibo_apert_agb` | homologacion | desarrollo, produccion | 🔴 CRÍTICO |
| `tensoft.reporte_facturacion_comercial` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.reportes_adicionales` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.sol_ruta_oid` | desarrollo | homologacion, produccion | 🔴 CRÍTICO |
| `tensoft.temp_nro_doc` | desarrollo, produccion | homologacion | 🟠 MEDIO |

## 📊 Matriz de Comparación

| Comparación | Tablas Solo Env1 | Tablas Solo Env2 | Dif. Columnas | Estado |
|-------------|------------------|------------------|---------------|--------|
| Desarrollo vs Homologacion | 15 | 1 | 16 | ❌ Diferencias significativas |
| Desarrollo vs Produccion | 11 | 1 | 7 | ❌ Diferencias significativas |
| Homologacion vs Produccion | 1 | 5 | 10 | ❌ Diferencias significativas |


## 🎯 Recomendaciones Finales

### Prioridad 1 - CRÍTICO 🔴
1. **Sincronizar con Producción:** Todas las diferencias con producción deben resolverse inmediatamente
2. **Tablas faltantes:** Crear tablas que faltan en producción desde desarrollo/homologación

### Prioridad 2 - ALTO 🟡
1. **Alinear Desarrollo y Homologación:** Resolver diferencias entre estos ambientes
2. **Diferencias de columnas:** Sincronizar tipos de datos y columnas faltantes

### Prioridad 3 - MEDIO 🟠
1. **Documentar diferencias:** Mantener registro de diferencias intencionales
2. **Establecer proceso:** Crear procedimiento para mantener sincronización

### Plan de Acción Sugerido
1. **Fase 1:** Resolver diferencias críticas con producción
2. **Fase 2:** Alinear desarrollo y homologación
3. **Fase 3:** Implementar controles de sincronización
4. **Fase 4:** Establecer monitoreo continuo
