================================================================================
REPORTE DE DIFERENCIAS ENTRE ESQUEMAS
Desarrollo vs Homologación
================================================================================

1. DIFERENCIAS EN TABLAS
----------------------------------------

Tablas SOLO en DESARROLLO (15):
  - ressia.app_auxiliar_numera
  - ressia.lolo
  - ressia.rec
  - ressia.sol_hoja_ruta
  - ressia.sol_rel
  - tensoft.calendario_segmento
  - tensoft.ciudad_sigii_sol
  - tensoft.cotiz_extendida_v1
  - tensoft.modalidad
  - tensoft.provin_sigii_sol
  - tensoft.receipts
  - tensoft.reporte_facturacion_comercial
  - tensoft.reportes_adicionales
  - tensoft.sol_ruta_oid
  - tensoft.temp_nro_doc

Tablas SOLO en HOMOLOGACIÓN (1):
  - tensoft.recibo_apert_agb

Tablas COMUNES: 1495

2. DIFERENCIAS EN COLUMNAS (15 tablas afectadas)
----------------------------------------

Tabla: ressia.cli_var_normalizada
  Columnas SOLO en desarrollo:
    - estado_sol
  Diferencias de tipo:
    - codigo_direccion: DEV(varchar(200)) vs HOM(varchar(20))

Tabla: ressia.cloud_tipo_linea_v2
  Diferencias de tipo:
    - cai_cae: DEV(varchar(50)) vs HOM(char(30))

Tabla: lobatose.oracle_transac_clie
  Diferencias de tipo:
    - cai_cae: DEV(varchar(50)) vs HOM(char(30))

Tabla: tensoft.cliente
  Columnas SOLO en desarrollo:
    - estado_sol

Tabla: tensoft.factura_historico_sgd
  Columnas SOLO en desarrollo:
    - crc
  Columnas SOLO en homologación:
    - cdc

Tabla: tensoft.hoja_ruta
  Columnas SOLO en desarrollo:
    - cod_ent_serv_dest
    - cod_ent_serv_orig
    - cod_pto_serv_dest
    - cod_pto_serv_orig
    - codigo_producto
    - desc_ent_serv_dest
    - desc_ent_serv_orig
    - desc_pto_serv_dest
    - desc_pto_serv_orig
    - descripcion_producto

Tabla: ressia.cloud_tipo_tax_v2
  Diferencias de tipo:
    - cai_cae: DEV(varchar(50)) vs HOM(char(30))

Tabla: ressia.oracle_ana_interna
  Columnas SOLO en desarrollo:
    - py_imputa

Tabla: ressia.oracle_transaccion_auxiliar_v2
  Diferencias de tipo:
    - cai_cae: DEV(varchar(50)) vs HOM(char(30))

Tabla: ressia.itbk_publ_docu_impre_v01
  Columnas SOLO en desarrollo:
    - estado_sgd

Tabla: tensoft.centro
  Columnas SOLO en desarrollo:
    - estado_sol

Tabla: tensoft.cli_variante
  Columnas SOLO en desarrollo:
    - estado_sol

Tabla: ressia.oracle_transaccion_auxiliar_v3
  Diferencias de tipo:
    - cai_cae: DEV(varchar(50)) vs HOM(char(30))

Tabla: tensoft.factura
  Columnas SOLO en desarrollo:
    - default

Tabla: tensoft.fac_impresion
  Columnas SOLO en desarrollo:
    - estado_sgd
  Diferencias de tipo:
    - codigo_cae: DEV(varchar(100)) vs HOM(varchar(14))
    - motivo_afip: DEV(char(100)) vs HOM(varchar(3))

3. DIFERENCIAS EN STORED PROCEDURES
----------------------------------------

Procedures COMUNES: 0

4. DIFERENCIAS EN ROW TYPES
----------------------------------------

Row Types COMUNES: 0

5. RESUMEN
----------------------------------------
Total tablas en desarrollo: 1510
Total tablas en homologación: 1496
Total procedures en desarrollo: 0
Total procedures en homologación: 0
Total row types en desarrollo: 0
Total row types en homologación: 0