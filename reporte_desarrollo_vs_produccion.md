# 📊 Reporte de Comparación de Esquemas
## Desarrollo vs Produccion

**Fecha de generación:** 2025-08-26 09:55:36

## 📋 Resumen Ejecutivo

| Métrica | Valor |
|---------|-------|
| Tablas en desarrollo | 1,510 |
| Tablas en produccion | 1,500 |
| Tablas comunes | 1,499 |
| Tablas solo en desarrollo | 11 |
| Tablas solo en produccion | 1 |
| Tablas con diferencias en columnas | 7 |

### Estado General: ❌ **LOS ESQUEMAS NO SON IDÉNTICOS**
### Nivel de Criticidad: 🔴 CRÍTICO

## 🚨 Tablas Solo en Desarrollo (11)

| Tabla | Schema | Criticidad |
|-------|--------|------------|
| `sol_hoja_ruta` | `ressia` | 🟠 MEDIO |
| `sol_rel` | `ressia` | 🟠 MEDIO |
| `calendario_segmento` | `tensoft` | 🟡 ALTO |
| `ciudad_sigii_sol` | `tensoft` | 🟡 ALTO |
| `cotiz_extendida_v1` | `tensoft` | 🟡 ALTO |
| `modalidad` | `tensoft` | 🟡 ALTO |
| `provin_sigii_sol` | `tensoft` | 🟡 ALTO |
| `receipts` | `tensoft` | 🟡 ALTO |
| `reporte_facturacion_comercial` | `tensoft` | 🟡 ALTO |
| `reportes_adicionales` | `tensoft` | 🟡 ALTO |
| `sol_ruta_oid` | `tensoft` | 🟡 ALTO |

## 🚨 Tablas Solo en Produccion (1)

| Tabla | Schema | Criticidad |
|-------|--------|------------|
| `paso1` | `ressia` | 🟠 MEDIO |

## 🔧 Diferencias en Columnas (7 tablas afectadas)

### 📋 `tensoft.hoja_ruta`

**Columnas solo en desarrollo:**
- `cod_ent_serv_dest` - `varchar(10)`
- `cod_ent_serv_orig` - `varchar(10)`
- `cod_pto_serv_dest` - `varchar(10)`
- `cod_pto_serv_orig` - `varchar(10)`
- `codigo_producto` - `varchar(10)`
- `desc_ent_serv_dest` - `varchar(30)`
- `desc_ent_serv_orig` - `varchar(50)`
- `desc_pto_serv_dest` - `varchar(30)`
- `desc_pto_serv_orig` - `varchar(30)`
- `descripcion_producto` - `varchar(30)`

### 📋 `tensoft.centro`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

### 📋 `tensoft.factura`

**Columnas solo en desarrollo:**
- `default` - `'A'`

### 📋 `ressia.itbk_publ_docu_impre_v01`

**Columnas solo en desarrollo:**
- `estado_sgd` - `varchar(25)`

### 📋 `tensoft.cliente`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

### 📋 `ressia.cli_var_normalizada`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

### 📋 `tensoft.cli_variante`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

## 🎯 Recomendaciones de Sincronización

### 🔴 CRÍTICO Crear tablas faltantes en Produccion
- **Acción:** Crear 11 tablas que existen en desarrollo pero no en produccion
- **Impacto:** Funcionalidades pueden fallar si estas tablas son requeridas

### 🟠 MEDIO Evaluar tablas extra en Produccion
- **Acción:** Revisar 1 tablas que existen solo en produccion
- **Impacto:** Determinar si deben agregarse a desarrollo o eliminarse de produccion

### 🔴 CRÍTICO Sincronizar columnas
- **Acción:** Revisar y sincronizar diferencias en 7 tablas
- **Impacto:** Consultas y aplicaciones pueden fallar por columnas faltantes o tipos incorrectos
