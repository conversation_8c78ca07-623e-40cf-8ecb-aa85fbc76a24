# 📊 Reporte de Comparación de Esquemas
## Desarrollo vs Homologacion

**Fecha de generación:** 2025-08-26 09:55:36

## 📋 Resumen Ejecutivo

| Métrica | Valor |
|---------|-------|
| Tablas en desarrollo | 1,510 |
| Tablas en homologacion | 1,496 |
| Tablas comunes | 1,495 |
| Tablas solo en desarrollo | 15 |
| Tablas solo en homologacion | 1 |
| Tablas con diferencias en columnas | 16 |

### Estado General: ❌ **LOS ESQUEMAS NO SON IDÉNTICOS**
### Nivel de Criticidad: 🔴 CRÍTICO

## 🚨 Tablas Solo en Desarrollo (15)

| Tabla | Schema | Criticidad |
|-------|--------|------------|
| `app_auxiliar_numera` | `ressia` | 🟠 MEDIO |
| `lolo` | `ressia` | 🟠 MEDIO |
| `rec` | `ressia` | 🟠 MEDIO |
| `sol_hoja_ruta` | `ressia` | 🟠 MEDIO |
| `sol_rel` | `ressia` | 🟠 MEDIO |
| `calendario_segmento` | `tensoft` | 🟡 ALTO |
| `ciudad_sigii_sol` | `tensoft` | 🟡 ALTO |
| `cotiz_extendida_v1` | `tensoft` | 🟡 ALTO |
| `modalidad` | `tensoft` | 🟡 ALTO |
| `provin_sigii_sol` | `tensoft` | 🟡 ALTO |
| `receipts` | `tensoft` | 🟡 ALTO |
| `reporte_facturacion_comercial` | `tensoft` | 🟡 ALTO |
| `reportes_adicionales` | `tensoft` | 🟡 ALTO |
| `sol_ruta_oid` | `tensoft` | 🟡 ALTO |
| `temp_nro_doc` | `tensoft` | 🟡 ALTO |

## 🚨 Tablas Solo en Homologacion (1)

| Tabla | Schema | Criticidad |
|-------|--------|------------|
| `recibo_apert_agb` | `tensoft` | 🟡 ALTO |

## 🔧 Diferencias en Columnas (16 tablas afectadas)

### 📋 `tensoft.hoja_ruta`

**Columnas solo en desarrollo:**
- `cod_ent_serv_dest` - `varchar(10)`
- `cod_ent_serv_orig` - `varchar(10)`
- `cod_pto_serv_dest` - `varchar(10)`
- `cod_pto_serv_orig` - `varchar(10)`
- `codigo_producto` - `varchar(10)`
- `desc_ent_serv_dest` - `varchar(30)`
- `desc_ent_serv_orig` - `varchar(50)`
- `desc_pto_serv_dest` - `varchar(30)`
- `desc_pto_serv_orig` - `varchar(30)`
- `descripcion_producto` - `varchar(30)`

### 📋 `tensoft.centro`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

### 📋 `tensoft.factura`

**Columnas solo en desarrollo:**
- `default` - `'A'`

### 📋 `tensoft.calendario`

**Columnas solo en desarrollo:**
- `unique` - `(empresa,periodo_facturacion,id_grupo) constraint "tensoft".unique_calendario`

### 📋 `lobatose.oracle_transac_clie`

**Diferencias de tipo:**

| Columna | Desarrollo | Homologacion | Criticidad |
|---------|-----------|-------------|------------|
| `cai_cae` | `varchar(50)` | `char(30)` | 🟡 ALTO |

### 📋 `ressia.oracle_transaccion_auxiliar_v3`

**Diferencias de tipo:**

| Columna | Desarrollo | Homologacion | Criticidad |
|---------|-----------|-------------|------------|
| `cai_cae` | `varchar(50)` | `char(30)` | 🟡 ALTO |

### 📋 `tensoft.fac_impresion`

**Columnas solo en desarrollo:**
- `estado_sgd` - `varchar(25)`

**Diferencias de tipo:**

| Columna | Desarrollo | Homologacion | Criticidad |
|---------|-----------|-------------|------------|
| `codigo_cae` | `varchar(100)` | `varchar(14)` | 🟡 ALTO |
| `motivo_afip` | `char(100)` | `varchar(3)` | 🟡 ALTO |

### 📋 `ressia.itbk_publ_docu_impre_v01`

**Columnas solo en desarrollo:**
- `estado_sgd` - `varchar(25)`

### 📋 `ressia.cloud_tipo_linea_v2`

**Diferencias de tipo:**

| Columna | Desarrollo | Homologacion | Criticidad |
|---------|-----------|-------------|------------|
| `cai_cae` | `varchar(50)` | `char(30)` | 🟡 ALTO |

### 📋 `ressia.oracle_ana_interna`

**Columnas solo en desarrollo:**
- `py_imputa` - `varchar(20)`

### 📋 `ressia.oracle_transaccion_auxiliar_v2`

**Diferencias de tipo:**

| Columna | Desarrollo | Homologacion | Criticidad |
|---------|-----------|-------------|------------|
| `cai_cae` | `varchar(50)` | `char(30)` | 🟡 ALTO |

### 📋 `ressia.cloud_tipo_tax_v2`

**Diferencias de tipo:**

| Columna | Desarrollo | Homologacion | Criticidad |
|---------|-----------|-------------|------------|
| `cai_cae` | `varchar(50)` | `char(30)` | 🟡 ALTO |

### 📋 `tensoft.cliente`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

### 📋 `tensoft.factura_historico_sgd`

**Columnas solo en desarrollo:**
- `crc` - `varchar(100)`

**Columnas solo en homologacion:**
- `cdc` - `varchar(100)`

### 📋 `ressia.cli_var_normalizada`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

**Diferencias de tipo:**

| Columna | Desarrollo | Homologacion | Criticidad |
|---------|-----------|-------------|------------|
| `codigo_direccion` | `varchar(200)` | `varchar(20)` | 🟡 ALTO |

### 📋 `tensoft.cli_variante`

**Columnas solo en desarrollo:**
- `estado_sol` - `varchar(15)`

## 🎯 Recomendaciones de Sincronización

### 🔴 CRÍTICO Crear tablas faltantes en Homologacion
- **Acción:** Crear 15 tablas que existen en desarrollo pero no en homologacion
- **Impacto:** Funcionalidades pueden fallar si estas tablas son requeridas

### 🟠 MEDIO Evaluar tablas extra en Homologacion
- **Acción:** Revisar 1 tablas que existen solo en homologacion
- **Impacto:** Determinar si deben agregarse a desarrollo o eliminarse de homologacion

### 🔴 CRÍTICO Sincronizar columnas
- **Acción:** Revisar y sincronizar diferencias en 16 tablas
- **Impacto:** Consultas y aplicaciones pueden fallar por columnas faltantes o tipos incorrectos
