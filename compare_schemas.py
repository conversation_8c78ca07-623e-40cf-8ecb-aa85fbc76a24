#!/usr/bin/env python3
"""
Script para comparar dos esquemas de base de datos Informix
Analiza diferencias en tablas, columnas y stored procedures
"""

import re
import sys
from collections import defaultdict
from typing import Dict, List, Set, Tuple

class SchemaComparator:
    def __init__(self):
        self.tables_dev = {}
        self.tables_hom = {}
        self.procedures_dev = set()
        self.procedures_hom = set()
        self.row_types_dev = set()
        self.row_types_hom = set()
        
    def parse_table_definition(self, content: str) -> Dict[str, Dict]:
        """Extrae definiciones de tablas del contenido del schema"""
        tables = {}
        
        # Patrón para encontrar definiciones de tablas
        table_pattern = r'create table "([^"]+)"\.([^\s]+)\s*\(\s*(.*?)\s*\);'
        
        # Buscar todas las tablas
        for match in re.finditer(table_pattern, content, re.DOTALL | re.IGNORECASE):
            schema_name = match.group(1)
            table_name = match.group(2)
            columns_def = match.group(3)
            
            full_table_name = f"{schema_name}.{table_name}"
            
            # Parsear columnas
            columns = self.parse_columns(columns_def)
            
            tables[full_table_name] = {
                'columns': columns,
                'schema': schema_name,
                'table': table_name
            }
            
        return tables
    
    def parse_columns(self, columns_def: str) -> Dict[str, Dict]:
        """Parsea la definición de columnas de una tabla"""
        columns = {}
        
        # Limpiar y dividir por líneas
        lines = columns_def.strip().split('\n')
        current_column = None
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('primary key') or line.startswith('check'):
                continue
                
            # Buscar definición de columna
            col_match = re.match(r'(\w+)\s+([^,]+?)(?:,\s*)?$', line)
            if col_match:
                col_name = col_match.group(1)
                col_def = col_match.group(2).strip()
                
                columns[col_name] = {
                    'definition': col_def,
                    'type': self.extract_column_type(col_def)
                }
                
        return columns
    
    def extract_column_type(self, col_def: str) -> str:
        """Extrae el tipo de dato de la definición de columna"""
        # Remover constraints y defaults
        type_part = re.split(r'\s+(?:not null|default|constraint)', col_def)[0]
        return type_part.strip()
    
    def parse_procedures(self, content: str) -> Set[str]:
        """Extrae nombres de stored procedures"""
        procedures = set()
        
        # Patrones para diferentes tipos de procedures
        patterns = [
            r'create procedure\s+"?([^"\s(]+)"?\s*\(',
            r'create function\s+"?([^"\s(]+)"?\s*\(',
        ]
        
        for pattern in patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                proc_name = match.group(1)
                procedures.add(proc_name)
                
        return procedures
    
    def parse_row_types(self, content: str) -> Set[str]:
        """Extrae nombres de row types"""
        row_types = set()
        
        pattern = r'create row type\s+"?([^"\s]+)"?\s*\('
        for match in re.finditer(pattern, content, re.IGNORECASE):
            row_type_name = match.group(1)
            row_types.add(row_type_name)
            
        return row_types
    
    def load_schema(self, filename: str, is_dev: bool = True):
        """Carga y parsea un archivo de schema"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(filename, 'r', encoding='latin-1') as f:
                content = f.read()
        
        tables = self.parse_table_definition(content)
        procedures = self.parse_procedures(content)
        row_types = self.parse_row_types(content)
        
        if is_dev:
            self.tables_dev = tables
            self.procedures_dev = procedures
            self.row_types_dev = row_types
        else:
            self.tables_hom = tables
            self.procedures_hom = procedures
            self.row_types_hom = row_types
    
    def compare_tables(self) -> Dict:
        """Compara las tablas entre los dos esquemas"""
        dev_tables = set(self.tables_dev.keys())
        hom_tables = set(self.tables_hom.keys())
        
        only_in_dev = dev_tables - hom_tables
        only_in_hom = hom_tables - dev_tables
        common_tables = dev_tables & hom_tables
        
        column_differences = {}
        
        for table in common_tables:
            dev_cols = set(self.tables_dev[table]['columns'].keys())
            hom_cols = set(self.tables_hom[table]['columns'].keys())
            
            cols_only_dev = dev_cols - hom_cols
            cols_only_hom = hom_cols - dev_cols
            common_cols = dev_cols & hom_cols
            
            type_differences = []
            for col in common_cols:
                dev_type = self.tables_dev[table]['columns'][col]['type']
                hom_type = self.tables_hom[table]['columns'][col]['type']
                
                if dev_type != hom_type:
                    type_differences.append({
                        'column': col,
                        'dev_type': dev_type,
                        'hom_type': hom_type
                    })
            
            if cols_only_dev or cols_only_hom or type_differences:
                column_differences[table] = {
                    'only_in_dev': cols_only_dev,
                    'only_in_hom': cols_only_hom,
                    'type_differences': type_differences
                }
        
        return {
            'only_in_dev': only_in_dev,
            'only_in_hom': only_in_hom,
            'common_tables': len(common_tables),
            'column_differences': column_differences
        }
    
    def compare_procedures(self) -> Dict:
        """Compara los stored procedures entre los dos esquemas"""
        only_in_dev = self.procedures_dev - self.procedures_hom
        only_in_hom = self.procedures_hom - self.procedures_dev
        common = self.procedures_dev & self.procedures_hom
        
        return {
            'only_in_dev': only_in_dev,
            'only_in_hom': only_in_hom,
            'common': len(common)
        }
    
    def compare_row_types(self) -> Dict:
        """Compara los row types entre los dos esquemas"""
        only_in_dev = self.row_types_dev - self.row_types_hom
        only_in_hom = self.row_types_hom - self.row_types_dev
        common = self.row_types_dev & self.row_types_hom
        
        return {
            'only_in_dev': only_in_dev,
            'only_in_hom': only_in_hom,
            'common': len(common)
        }
    
    def generate_report(self) -> str:
        """Genera un reporte completo de las diferencias"""
        report = []
        report.append("=" * 80)
        report.append("REPORTE DE DIFERENCIAS ENTRE ESQUEMAS")
        report.append("Desarrollo vs Homologación")
        report.append("=" * 80)
        
        # Comparar tablas
        table_diff = self.compare_tables()
        report.append("\n1. DIFERENCIAS EN TABLAS")
        report.append("-" * 40)
        
        if table_diff['only_in_dev']:
            report.append(f"\nTablas SOLO en DESARROLLO ({len(table_diff['only_in_dev'])}):")
            for table in sorted(table_diff['only_in_dev']):
                report.append(f"  - {table}")
        
        if table_diff['only_in_hom']:
            report.append(f"\nTablas SOLO en HOMOLOGACIÓN ({len(table_diff['only_in_hom'])}):")
            for table in sorted(table_diff['only_in_hom']):
                report.append(f"  - {table}")
        
        report.append(f"\nTablas COMUNES: {table_diff['common_tables']}")
        
        # Diferencias en columnas
        if table_diff['column_differences']:
            report.append(f"\n2. DIFERENCIAS EN COLUMNAS ({len(table_diff['column_differences'])} tablas afectadas)")
            report.append("-" * 40)
            
            for table, diffs in table_diff['column_differences'].items():
                report.append(f"\nTabla: {table}")
                
                if diffs['only_in_dev']:
                    report.append(f"  Columnas SOLO en desarrollo:")
                    for col in sorted(diffs['only_in_dev']):
                        report.append(f"    - {col}")
                
                if diffs['only_in_hom']:
                    report.append(f"  Columnas SOLO en homologación:")
                    for col in sorted(diffs['only_in_hom']):
                        report.append(f"    - {col}")
                
                if diffs['type_differences']:
                    report.append(f"  Diferencias de tipo:")
                    for diff in diffs['type_differences']:
                        report.append(f"    - {diff['column']}: DEV({diff['dev_type']}) vs HOM({diff['hom_type']})")
        
        # Comparar procedures
        proc_diff = self.compare_procedures()
        report.append(f"\n3. DIFERENCIAS EN STORED PROCEDURES")
        report.append("-" * 40)
        
        if proc_diff['only_in_dev']:
            report.append(f"\nProcedures SOLO en DESARROLLO ({len(proc_diff['only_in_dev'])}):")
            for proc in sorted(proc_diff['only_in_dev']):
                report.append(f"  - {proc}")
        
        if proc_diff['only_in_hom']:
            report.append(f"\nProcedures SOLO en HOMOLOGACIÓN ({len(proc_diff['only_in_hom'])}):")
            for proc in sorted(proc_diff['only_in_hom']):
                report.append(f"  - {proc}")
        
        report.append(f"\nProcedures COMUNES: {proc_diff['common']}")
        
        # Comparar row types
        rt_diff = self.compare_row_types()
        report.append(f"\n4. DIFERENCIAS EN ROW TYPES")
        report.append("-" * 40)
        
        if rt_diff['only_in_dev']:
            report.append(f"\nRow Types SOLO en DESARROLLO ({len(rt_diff['only_in_dev'])}):")
            for rt in sorted(rt_diff['only_in_dev']):
                report.append(f"  - {rt}")
        
        if rt_diff['only_in_hom']:
            report.append(f"\nRow Types SOLO en HOMOLOGACIÓN ({len(rt_diff['only_in_hom'])}):")
            for rt in sorted(rt_diff['only_in_hom']):
                report.append(f"  - {rt}")
        
        report.append(f"\nRow Types COMUNES: {rt_diff['common']}")
        
        # Resumen
        report.append(f"\n5. RESUMEN")
        report.append("-" * 40)
        report.append(f"Total tablas en desarrollo: {len(self.tables_dev)}")
        report.append(f"Total tablas en homologación: {len(self.tables_hom)}")
        report.append(f"Total procedures en desarrollo: {len(self.procedures_dev)}")
        report.append(f"Total procedures en homologación: {len(self.procedures_hom)}")
        report.append(f"Total row types en desarrollo: {len(self.row_types_dev)}")
        report.append(f"Total row types en homologación: {len(self.row_types_hom)}")
        
        return "\n".join(report)

def main():
    comparator = SchemaComparator()
    
    print("Cargando esquema de desarrollo...")
    comparator.load_schema('schemaTCJ_desarrollo.txt', is_dev=True)
    
    print("Cargando esquema de homologación...")
    comparator.load_schema('schemaTCJ_homologacion.txt', is_dev=False)
    
    print("Generando reporte de diferencias...")
    report = comparator.generate_report()
    
    # Guardar reporte en archivo
    with open('reporte_diferencias_schemas.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("Reporte generado en: reporte_diferencias_schemas.txt")
    print("\n" + "="*50)
    print(report)

if __name__ == "__main__":
    main()
