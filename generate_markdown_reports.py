#!/usr/bin/env python3
"""
Script para generar reportes de comparación de esquemas en formato Markdown
Compara desarrollo, homologación y producción
"""

import re
import sys
from collections import defaultdict
from typing import Dict, List, Set, Tuple
from datetime import datetime

class MarkdownSchemaComparator:
    def __init__(self):
        self.schemas = {}
        
    def parse_table_definition(self, content: str) -> Dict[str, Dict]:
        """Extrae definiciones de tablas del contenido del schema"""
        tables = {}
        
        # Patrón mejorado para encontrar definiciones de tablas
        table_pattern = r'create table\s+"([^"]+)"\.([^\s]+)\s*\(\s*(.*?)\s*\);'
        
        for match in re.finditer(table_pattern, content, re.DOTALL | re.IGNORECASE):
            schema_name = match.group(1)
            table_name = match.group(2)
            columns_def = match.group(3)
            
            full_table_name = f"{schema_name}.{table_name}"
            
            # Parsear columnas
            columns = self.parse_columns(columns_def)
            
            tables[full_table_name] = {
                'columns': columns,
                'schema': schema_name,
                'table': table_name,
                'column_count': len(columns)
            }
            
        return tables
    
    def parse_columns(self, columns_def: str) -> Dict[str, Dict]:
        """Parsea la definición de columnas de una tabla"""
        columns = {}
        
        # Dividir por líneas y procesar cada una
        lines = columns_def.strip().split('\n')
        
        for line in lines:
            line = line.strip().rstrip(',')
            if not line or line.startswith('primary key') or line.startswith('check') or line.startswith('constraint'):
                continue
                
            # Buscar definición de columna
            parts = line.split()
            if len(parts) >= 2:
                col_name = parts[0]
                col_def = ' '.join(parts[1:])
                
                columns[col_name] = {
                    'definition': col_def,
                    'type': self.extract_column_type(col_def),
                    'nullable': 'not null' not in col_def.lower(),
                    'has_default': 'default' in col_def.lower()
                }
                
        return columns
    
    def extract_column_type(self, col_def: str) -> str:
        """Extrae el tipo de dato de la definición de columna"""
        # Remover constraints y defaults
        type_part = re.split(r'\s+(?:not null|default|constraint)', col_def, flags=re.IGNORECASE)[0]
        return type_part.strip()
    
    def parse_procedures(self, content: str) -> Set[str]:
        """Extrae nombres de stored procedures"""
        procedures = set()
        
        patterns = [
            r'create\s+procedure\s+"?([^"\s(]+)"?\s*\(',
            r'create\s+function\s+"?([^"\s(]+)"?\s*\(',
        ]
        
        for pattern in patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                proc_name = match.group(1)
                procedures.add(proc_name)
                
        return procedures
    
    def parse_row_types(self, content: str) -> Set[str]:
        """Extrae nombres de row types"""
        row_types = set()
        
        pattern = r'create\s+row\s+type\s+"?([^"\s]+)"?\s*\('
        for match in re.finditer(pattern, content, re.IGNORECASE):
            row_type_name = match.group(1)
            row_types.add(row_type_name)
            
        return row_types
    
    def load_schema(self, filename: str, environment: str):
        """Carga y parsea un archivo de schema"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(filename, 'r', encoding='latin-1') as f:
                content = f.read()
        
        tables = self.parse_table_definition(content)
        procedures = self.parse_procedures(content)
        row_types = self.parse_row_types(content)
        
        self.schemas[environment] = {
            'tables': tables,
            'procedures': procedures,
            'row_types': row_types,
            'filename': filename
        }
    
    def compare_environments(self, env1: str, env2: str) -> Dict:
        """Compara dos ambientes y retorna las diferencias"""
        tables1 = set(self.schemas[env1]['tables'].keys())
        tables2 = set(self.schemas[env2]['tables'].keys())
        
        only_in_env1 = tables1 - tables2
        only_in_env2 = tables2 - tables1
        common_tables = tables1 & tables2
        
        column_differences = {}
        
        for table in common_tables:
            cols1 = set(self.schemas[env1]['tables'][table]['columns'].keys())
            cols2 = set(self.schemas[env2]['tables'][table]['columns'].keys())
            
            cols_only_env1 = cols1 - cols2
            cols_only_env2 = cols2 - cols1
            common_cols = cols1 & cols2
            
            type_differences = []
            for col in common_cols:
                type1 = self.schemas[env1]['tables'][table]['columns'][col]['type']
                type2 = self.schemas[env2]['tables'][table]['columns'][col]['type']
                
                if type1 != type2:
                    type_differences.append({
                        'column': col,
                        'env1_type': type1,
                        'env2_type': type2
                    })
            
            if cols_only_env1 or cols_only_env2 or type_differences:
                column_differences[table] = {
                    'only_in_env1': cols_only_env1,
                    'only_in_env2': cols_only_env2,
                    'type_differences': type_differences
                }
        
        # Comparar procedures
        procs1 = self.schemas[env1]['procedures']
        procs2 = self.schemas[env2]['procedures']
        
        proc_only_env1 = procs1 - procs2
        proc_only_env2 = procs2 - procs1
        proc_common = procs1 & procs2
        
        # Comparar row types
        rt1 = self.schemas[env1]['row_types']
        rt2 = self.schemas[env2]['row_types']
        
        rt_only_env1 = rt1 - rt2
        rt_only_env2 = rt2 - rt1
        rt_common = rt1 & rt2
        
        return {
            'tables': {
                'only_in_env1': only_in_env1,
                'only_in_env2': only_in_env2,
                'common': len(common_tables),
                'column_differences': column_differences
            },
            'procedures': {
                'only_in_env1': proc_only_env1,
                'only_in_env2': proc_only_env2,
                'common': len(proc_common)
            },
            'row_types': {
                'only_in_env1': rt_only_env1,
                'only_in_env2': rt_only_env2,
                'common': len(rt_common)
            }
        }
    
    def get_criticality_level(self, diff_type: str, count: int) -> str:
        """Determina el nivel de criticidad basado en el tipo y cantidad de diferencias"""
        if diff_type == 'missing_tables':
            if count > 10:
                return "🔴 CRÍTICO"
            elif count > 5:
                return "🟡 ALTO"
            elif count > 0:
                return "🟠 MEDIO"
        elif diff_type == 'column_differences':
            if count > 5:
                return "🔴 CRÍTICO"
            elif count > 2:
                return "🟡 ALTO"
            elif count > 0:
                return "🟠 MEDIO"
        elif diff_type == 'type_differences':
            if count > 0:
                return "🟡 ALTO"
        
        return "🟢 BAJO"
    
    def generate_markdown_report(self, env1: str, env2: str, differences: Dict) -> str:
        """Genera un reporte en formato Markdown"""
        report = []
        
        # Header
        report.append(f"# 📊 Reporte de Comparación de Esquemas")
        report.append(f"## {env1.title()} vs {env2.title()}")
        report.append(f"")
        report.append(f"**Fecha de generación:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"")
        
        # Resumen ejecutivo
        total_tables_env1 = len(self.schemas[env1]['tables'])
        total_tables_env2 = len(self.schemas[env2]['tables'])
        tables_only_env1 = len(differences['tables']['only_in_env1'])
        tables_only_env2 = len(differences['tables']['only_in_env2'])
        common_tables = differences['tables']['common']
        column_diffs = len(differences['tables']['column_differences'])
        
        report.append("## 📋 Resumen Ejecutivo")
        report.append("")
        report.append("| Métrica | Valor |")
        report.append("|---------|-------|")
        report.append(f"| Tablas en {env1} | {total_tables_env1:,} |")
        report.append(f"| Tablas en {env2} | {total_tables_env2:,} |")
        report.append(f"| Tablas comunes | {common_tables:,} |")
        report.append(f"| Tablas solo en {env1} | {tables_only_env1:,} |")
        report.append(f"| Tablas solo en {env2} | {tables_only_env2:,} |")
        report.append(f"| Tablas con diferencias en columnas | {column_diffs:,} |")
        report.append("")
        
        # Estado general
        if tables_only_env1 > 0 or tables_only_env2 > 0 or column_diffs > 0:
            status = "❌ **LOS ESQUEMAS NO SON IDÉNTICOS**"
            criticality = self.get_criticality_level('missing_tables', max(tables_only_env1, tables_only_env2))
        else:
            status = "✅ **LOS ESQUEMAS SON IDÉNTICOS**"
            criticality = "🟢 BAJO"
        
        report.append(f"### Estado General: {status}")
        report.append(f"### Nivel de Criticidad: {criticality}")
        report.append("")
        
        # Diferencias en tablas
        if tables_only_env1 > 0:
            report.append(f"## 🚨 Tablas Solo en {env1.title()} ({tables_only_env1})")
            report.append("")
            report.append("| Tabla | Schema | Criticidad |")
            report.append("|-------|--------|------------|")
            
            for table in sorted(differences['tables']['only_in_env1']):
                schema, table_name = table.split('.', 1)
                criticality = "🟡 ALTO" if schema == "tensoft" else "🟠 MEDIO"
                report.append(f"| `{table_name}` | `{schema}` | {criticality} |")
            report.append("")
        
        if tables_only_env2 > 0:
            report.append(f"## 🚨 Tablas Solo en {env2.title()} ({tables_only_env2})")
            report.append("")
            report.append("| Tabla | Schema | Criticidad |")
            report.append("|-------|--------|------------|")
            
            for table in sorted(differences['tables']['only_in_env2']):
                schema, table_name = table.split('.', 1)
                criticality = "🟡 ALTO" if schema == "tensoft" else "🟠 MEDIO"
                report.append(f"| `{table_name}` | `{schema}` | {criticality} |")
            report.append("")
        
        # Diferencias en columnas
        if column_diffs > 0:
            report.append(f"## 🔧 Diferencias en Columnas ({column_diffs} tablas afectadas)")
            report.append("")

            for table, diffs in differences['tables']['column_differences'].items():
                schema, table_name = table.split('.', 1)
                report.append(f"### 📋 `{schema}.{table_name}`")
                report.append("")

                if diffs['only_in_env1']:
                    report.append(f"**Columnas solo en {env1}:**")
                    for col in sorted(diffs['only_in_env1']):
                        col_def = self.schemas[env1]['tables'][table]['columns'][col]['definition']
                        report.append(f"- `{col}` - `{col_def}`")
                    report.append("")

                if diffs['only_in_env2']:
                    report.append(f"**Columnas solo en {env2}:**")
                    for col in sorted(diffs['only_in_env2']):
                        col_def = self.schemas[env2]['tables'][table]['columns'][col]['definition']
                        report.append(f"- `{col}` - `{col_def}`")
                    report.append("")

                if diffs['type_differences']:
                    report.append("**Diferencias de tipo:**")
                    report.append("")
                    report.append("| Columna | " + env1.title() + " | " + env2.title() + " | Criticidad |")
                    report.append("|---------|" + "-" * len(env1) + "-|" + "-" * len(env2) + "-|------------|")

                    for diff in diffs['type_differences']:
                        criticality = self.get_criticality_level('type_differences', 1)
                        report.append(f"| `{diff['column']}` | `{diff['env1_type']}` | `{diff['env2_type']}` | {criticality} |")
                    report.append("")

        # Diferencias en stored procedures
        procs_env1 = len(differences['procedures']['only_in_env1'])
        procs_env2 = len(differences['procedures']['only_in_env2'])

        if procs_env1 > 0 or procs_env2 > 0:
            report.append("## 🔧 Diferencias en Stored Procedures")
            report.append("")

            if procs_env1 > 0:
                report.append(f"### Procedures solo en {env1.title()} ({procs_env1})")
                for proc in sorted(differences['procedures']['only_in_env1']):
                    report.append(f"- `{proc}`")
                report.append("")

            if procs_env2 > 0:
                report.append(f"### Procedures solo en {env2.title()} ({procs_env2})")
                for proc in sorted(differences['procedures']['only_in_env2']):
                    report.append(f"- `{proc}`")
                report.append("")

        # Diferencias en row types
        rt_env1 = len(differences['row_types']['only_in_env1'])
        rt_env2 = len(differences['row_types']['only_in_env2'])

        if rt_env1 > 0 or rt_env2 > 0:
            report.append("## 🔧 Diferencias en Row Types")
            report.append("")

            if rt_env1 > 0:
                report.append(f"### Row Types solo en {env1.title()} ({rt_env1})")
                for rt in sorted(differences['row_types']['only_in_env1']):
                    report.append(f"- `{rt}`")
                report.append("")

            if rt_env2 > 0:
                report.append(f"### Row Types solo en {env2.title()} ({rt_env2})")
                for rt in sorted(differences['row_types']['only_in_env2']):
                    report.append(f"- `{rt}`")
                report.append("")

        # Recomendaciones
        report.append("## 🎯 Recomendaciones de Sincronización")
        report.append("")

        if tables_only_env1 > 0:
            criticality = self.get_criticality_level('missing_tables', tables_only_env1)
            report.append(f"### {criticality} Crear tablas faltantes en {env2.title()}")
            report.append(f"- **Acción:** Crear {tables_only_env1} tablas que existen en {env1} pero no en {env2}")
            report.append(f"- **Impacto:** Funcionalidades pueden fallar si estas tablas son requeridas")
            report.append("")

        if tables_only_env2 > 0:
            criticality = self.get_criticality_level('missing_tables', tables_only_env2)
            report.append(f"### {criticality} Evaluar tablas extra en {env2.title()}")
            report.append(f"- **Acción:** Revisar {tables_only_env2} tablas que existen solo en {env2}")
            report.append(f"- **Impacto:** Determinar si deben agregarse a {env1} o eliminarse de {env2}")
            report.append("")

        if column_diffs > 0:
            criticality = self.get_criticality_level('column_differences', column_diffs)
            report.append(f"### {criticality} Sincronizar columnas")
            report.append(f"- **Acción:** Revisar y sincronizar diferencias en {column_diffs} tablas")
            report.append(f"- **Impacto:** Consultas y aplicaciones pueden fallar por columnas faltantes o tipos incorrectos")
            report.append("")

        return "\n".join(report)

    def generate_consolidated_report(self) -> str:
        """Genera un reporte consolidado comparando los tres ambientes"""
        report = []

        # Header
        report.append("# 📊 Reporte Consolidado de Esquemas")
        report.append("## Desarrollo vs Homologación vs Producción")
        report.append("")
        report.append(f"**Fecha de generación:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Resumen de todos los ambientes
        report.append("## 📋 Resumen General")
        report.append("")
        report.append("| Ambiente | Tablas | Procedures | Row Types |")
        report.append("|----------|--------|------------|-----------|")

        for env in ['desarrollo', 'homologacion', 'produccion']:
            tables_count = len(self.schemas[env]['tables'])
            procs_count = len(self.schemas[env]['procedures'])
            rt_count = len(self.schemas[env]['row_types'])
            report.append(f"| {env.title()} | {tables_count:,} | {procs_count:,} | {rt_count:,} |")

        report.append("")

        # Análisis de consistencia
        all_tables = set()
        for env in self.schemas:
            all_tables.update(self.schemas[env]['tables'].keys())

        report.append("## 🔍 Análisis de Consistencia")
        report.append("")

        # Tablas presentes en todos los ambientes
        tables_in_all = set(self.schemas['desarrollo']['tables'].keys())
        tables_in_all &= set(self.schemas['homologacion']['tables'].keys())
        tables_in_all &= set(self.schemas['produccion']['tables'].keys())

        report.append(f"### ✅ Tablas Consistentes en Todos los Ambientes: {len(tables_in_all):,}")
        report.append("")

        # Tablas con problemas de consistencia
        inconsistent_tables = {}

        for table in all_tables:
            environments = []
            if table in self.schemas['desarrollo']['tables']:
                environments.append('desarrollo')
            if table in self.schemas['homologacion']['tables']:
                environments.append('homologacion')
            if table in self.schemas['produccion']['tables']:
                environments.append('produccion')

            if len(environments) < 3:
                inconsistent_tables[table] = environments

        if inconsistent_tables:
            report.append(f"### ❌ Tablas Inconsistentes: {len(inconsistent_tables):,}")
            report.append("")
            report.append("| Tabla | Presente en | Falta en | Criticidad |")
            report.append("|-------|-------------|----------|------------|")

            for table, envs in sorted(inconsistent_tables.items()):
                missing_envs = [e for e in ['desarrollo', 'homologacion', 'produccion'] if e not in envs]
                present_str = ", ".join(envs)
                missing_str = ", ".join(missing_envs)

                # Determinar criticidad
                if 'produccion' in missing_envs:
                    criticality = "🔴 CRÍTICO"
                elif len(missing_envs) > 1:
                    criticality = "🟡 ALTO"
                else:
                    criticality = "🟠 MEDIO"

                schema, table_name = table.split('.', 1)
                report.append(f"| `{schema}.{table_name}` | {present_str} | {missing_str} | {criticality} |")

            report.append("")

        # Matriz de comparación
        report.append("## 📊 Matriz de Comparación")
        report.append("")

        comparisons = [
            ('desarrollo', 'homologacion'),
            ('desarrollo', 'produccion'),
            ('homologacion', 'produccion')
        ]

        report.append("| Comparación | Tablas Solo Env1 | Tablas Solo Env2 | Dif. Columnas | Estado |")
        report.append("|-------------|------------------|------------------|---------------|--------|")

        for env1, env2 in comparisons:
            diff = self.compare_environments(env1, env2)
            only_env1 = len(diff['tables']['only_in_env1'])
            only_env2 = len(diff['tables']['only_in_env2'])
            col_diffs = len(diff['tables']['column_differences'])

            if only_env1 == 0 and only_env2 == 0 and col_diffs == 0:
                status = "✅ Idénticos"
            elif only_env1 + only_env2 + col_diffs < 5:
                status = "🟡 Diferencias menores"
            else:
                status = "❌ Diferencias significativas"

            report.append(f"| {env1.title()} vs {env2.title()} | {only_env1} | {only_env2} | {col_diffs} | {status} |")

        report.append("")

        return "\n".join(report)

def main():
    comparator = MarkdownSchemaComparator()

    # Cargar todos los esquemas
    print("Cargando esquemas...")
    comparator.load_schema('schemaTCJ_desarrollo.txt', 'desarrollo')
    comparator.load_schema('schemaTCJ_homologacion.txt', 'homologacion')
    comparator.load_schema('schemaTCJ_produccion.txt', 'produccion')

    # Generar reportes de comparación individuales
    comparisons = [
        ('desarrollo', 'produccion'),
        ('homologacion', 'produccion'),
        ('desarrollo', 'homologacion')
    ]

    for env1, env2 in comparisons:
        print(f"Generando reporte: {env1} vs {env2}...")
        differences = comparator.compare_environments(env1, env2)
        report = comparator.generate_markdown_report(env1, env2, differences)

        filename = f"reporte_{env1}_vs_{env2}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"✅ Reporte guardado: {filename}")

    # Generar reporte consolidado
    print("Generando reporte consolidado...")
    consolidated_report = comparator.generate_consolidated_report()

    # Agregar recomendaciones finales al reporte consolidado
    consolidated_report += "\n\n## 🎯 Recomendaciones Finales\n\n"
    consolidated_report += "### Prioridad 1 - CRÍTICO 🔴\n"
    consolidated_report += "1. **Sincronizar con Producción:** Todas las diferencias con producción deben resolverse inmediatamente\n"
    consolidated_report += "2. **Tablas faltantes:** Crear tablas que faltan en producción desde desarrollo/homologación\n\n"

    consolidated_report += "### Prioridad 2 - ALTO 🟡\n"
    consolidated_report += "1. **Alinear Desarrollo y Homologación:** Resolver diferencias entre estos ambientes\n"
    consolidated_report += "2. **Diferencias de columnas:** Sincronizar tipos de datos y columnas faltantes\n\n"

    consolidated_report += "### Prioridad 3 - MEDIO 🟠\n"
    consolidated_report += "1. **Documentar diferencias:** Mantener registro de diferencias intencionales\n"
    consolidated_report += "2. **Establecer proceso:** Crear procedimiento para mantener sincronización\n\n"

    consolidated_report += "### Plan de Acción Sugerido\n"
    consolidated_report += "1. **Fase 1:** Resolver diferencias críticas con producción\n"
    consolidated_report += "2. **Fase 2:** Alinear desarrollo y homologación\n"
    consolidated_report += "3. **Fase 3:** Implementar controles de sincronización\n"
    consolidated_report += "4. **Fase 4:** Establecer monitoreo continuo\n"

    with open('reporte_consolidado_tres_ambientes.md', 'w', encoding='utf-8') as f:
        f.write(consolidated_report)

    print("✅ Reporte consolidado guardado: reporte_consolidado_tres_ambientes.md")
    print("\n🎉 Todos los reportes han sido generados exitosamente!")
    print("\nReportes generados:")
    print("- reporte_desarrollo_vs_produccion.md")
    print("- reporte_homologacion_vs_produccion.md")
    print("- reporte_desarrollo_vs_homologacion.md")
    print("- reporte_consolidado_tres_ambientes.md")

if __name__ == "__main__":
    main()
